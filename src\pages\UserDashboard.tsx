import React, { useState } from 'react';
import { Package, Clock, MapPin, Star, Calendar, Plus, Search } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { Link } from 'react-router-dom';

const UserDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('recent');

  // Mock data
  const recentOrders = [
    {
      id: 'QD12345678',
      status: 'Delivered',
      date: '2024-01-15',
      pickup: '123 Main St',
      delivery: '456 Oak Ave',
      cost: '₹90',
      rating: 5
    },
    {
      id: 'QD87654321',
      status: 'In Transit',
      date: '2024-01-16',
      pickup: '789 Pine St',
      delivery: '321 Elm Rd',
      cost: '₹75',
      rating: null
    },
    {
      id: 'QD13579246',
      status: 'Delivered',
      date: '2024-01-14',
      pickup: '555 Cedar Ln',
      delivery: '777 Maple Dr',
      cost: '₹120',
      rating: 4
    }
  ];

  const savedAddresses = [
    {
      id: 1,
      label: 'Home',
      address: '123 Main Street, Downtown, City - 400001',
      isDefault: true
    },
    {
      id: 2,
      label: 'Office',
      address: '456 Business Park, Commercial Area, City - 400020',
      isDefault: false
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 text-green-800';
      case 'In Transit':
        return 'bg-blue-100 text-blue-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Welcome back, {user?.name}!</h1>
          <p className="text-gray-600 mt-2">Manage your deliveries and account settings</p>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Link to="/book" className="bg-blue-600 text-white p-6 rounded-xl shadow-lg hover:bg-blue-700 transition-all transform hover:-translate-y-1">
            <div className="flex items-center space-x-3">
              <Plus className="h-8 w-8" />
              <div>
                <h3 className="font-semibold text-lg">Book Delivery</h3>
                <p className="text-blue-100">Schedule a new pickup</p>
              </div>
            </div>
          </Link>

          <Link to="/track" className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 border border-gray-200">
            <div className="flex items-center space-x-3">
              <Search className="h-8 w-8 text-blue-600" />
              <div>
                <h3 className="font-semibold text-lg text-gray-800">Track Package</h3>
                <p className="text-gray-600">Monitor your deliveries</p>
              </div>
            </div>
          </Link>

          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <div className="flex items-center space-x-3">
              <Package className="h-8 w-8 text-blue-600" />
              <div>
                <h3 className="font-semibold text-lg text-gray-800">Total Orders</h3>
                <p className="text-2xl font-bold text-blue-600">24</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-xl shadow-lg">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'recent', label: 'Recent Orders', icon: Clock },
                { id: 'addresses', label: 'Saved Addresses', icon: MapPin }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Recent Orders Tab */}
            {activeTab === 'recent' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-800">Recent Orders</h3>
                  <Link to="/book" className="text-blue-600 hover:text-blue-700 font-medium">
                    Book New Delivery
                  </Link>
                </div>
                
                {recentOrders.map((order) => (
                  <div key={order.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <span className="font-semibold text-gray-800">#{order.id}</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {order.status}
                          </span>
                          <span className="text-sm text-gray-500">
                            <Calendar className="h-4 w-4 inline mr-1" />
                            {order.date}
                          </span>
                        </div>
                        
                        <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">From:</span> {order.pickup}
                          </div>
                          <div>
                            <span className="font-medium">To:</span> {order.delivery}
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-4 lg:mt-0 lg:text-right">
                        <div className="text-lg font-semibold text-gray-800">{order.cost}</div>
                        {order.rating && (
                          <div className="flex items-center justify-end space-x-1 mt-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < order.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        )}
                        {order.status === 'In Transit' && (
                          <Link 
                            to={`/track?id=${order.id}`}
                            className="inline-block mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
                          >
                            Track Package
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Saved Addresses Tab */}
            {activeTab === 'addresses' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-800">Saved Addresses</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Add New Address
                  </button>
                </div>
                
                {savedAddresses.map((address) => (
                  <div key={address.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-semibold text-gray-800">{address.label}</h4>
                          {address.isDefault && (
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                              Default
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600">{address.address}</p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                          Edit
                        </button>
                        <button className="text-red-600 hover:text-red-700 text-sm font-medium">
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;