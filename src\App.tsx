import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import ToastProvider from './components/Toast';
import ErrorBoundary from './components/ErrorBoundary';
import ProtectedRoute, { AdminRoute, PublicRoute } from './components/ProtectedRoute';
import { useRealTimeUpdates } from './hooks/useRealTimeUpdates';
import { PerformanceMonitor } from './hooks/usePerformanceMonitoring';
import { useAuthStore } from './store/authStore';
import { initializeSecurity } from './utils/security';
import Home from './pages/Home';
import BookDelivery from './pages/BookDelivery';
import TrackPackage from './pages/TrackPackage';
import Login from './pages/Login';
import Register from './pages/Register';
import PartnerRegistration from './pages/PartnerRegistration';
import AdminDashboard from './pages/AdminDashboard';
import UserDashboard from './pages/UserDashboard';

// App component with real-time updates, security, and performance monitoring
const AppContent: React.FC = () => {
  const { isAuthenticated } = useAuthStore();

  // Initialize security measures
  useEffect(() => {
    initializeSecurity();
  }, []);

  // Enable real-time updates for authenticated users
  useRealTimeUpdates({
    enablePackageUpdates: isAuthenticated,
    enableNotifications: isAuthenticated,
    updateInterval: 30000 // 30 seconds
  });

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Header />
        <main className="flex-1">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/track" element={<TrackPackage />} />

            {/* Public routes (redirect if authenticated) */}
            <Route path="/login" element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            } />
            <Route path="/register" element={
              <PublicRoute>
                <Register />
              </PublicRoute>
            } />
            <Route path="/partner-registration" element={<PartnerRegistration />} />

            {/* Protected routes */}
            <Route path="/book" element={
              <ProtectedRoute>
                <BookDelivery />
              </ProtectedRoute>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute requiredRole="customer">
                <UserDashboard />
              </ProtectedRoute>
            } />

            {/* Admin routes */}
            <Route path="/admin" element={
              <AdminRoute>
                <AdminDashboard />
              </AdminRoute>
            } />
          </Routes>
        </main>
        <Footer />
        <ToastProvider />

        {/* Performance monitoring (development only) */}
        <PerformanceMonitor
          showDebugInfo={process.env.NODE_ENV === 'development'}
          onMetricsUpdate={(metrics) => {
            // In production, send to analytics service
            console.log('Performance metrics:', metrics);
          }}
        />
      </div>
    </ErrorBoundary>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <AppContent />
      </Router>
    </ErrorBoundary>
  );
}

export default App;