import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import ToastProvider from './components/Toast';
import ProtectedRoute, { AdminRoute, PublicRoute } from './components/ProtectedRoute';
import Home from './pages/Home';
import BookDelivery from './pages/BookDelivery';
import TrackPackage from './pages/TrackPackage';
import Login from './pages/Login';
import Register from './pages/Register';
import PartnerRegistration from './pages/PartnerRegistration';
import AdminDashboard from './pages/AdminDashboard';
import UserDashboard from './pages/UserDashboard';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Header />
        <main className="flex-1">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/track" element={<TrackPackage />} />

            {/* Public routes (redirect if authenticated) */}
            <Route path="/login" element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            } />
            <Route path="/register" element={
              <PublicRoute>
                <Register />
              </PublicRoute>
            } />
            <Route path="/partner-registration" element={<PartnerRegistration />} />

            {/* Protected routes */}
            <Route path="/book" element={
              <ProtectedRoute>
                <BookDelivery />
              </ProtectedRoute>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute requiredRole="customer">
                <UserDashboard />
              </ProtectedRoute>
            } />

            {/* Admin routes */}
            <Route path="/admin" element={
              <AdminRoute>
                <AdminDashboard />
              </AdminRoute>
            } />
          </Routes>
        </main>
        <Footer />
        <ToastProvider />
      </div>
    </Router>
  );
}

export default App;