import React from 'react';
import { Link } from 'react-router-dom';
import { Package, Clock, Shield, Truck, Star, CheckCircle, ArrowRight } from 'lucide-react';

const Home: React.FC = () => {
  const features = [
    {
      icon: <Clock className="h-8 w-8 text-blue-600" />,
      title: "Ultra-Fast Delivery",
      description: "Get your packages delivered within 2-4 hours in your local area"
    },
    {
      icon: <Shield className="h-8 w-8 text-blue-600" />,
      title: "Secure & Insured",
      description: "All deliveries are insured and tracked with real-time updates"
    },
    {
      icon: <Truck className="h-8 w-8 text-blue-600" />,
      title: "Local Network",
      description: "Powered by trusted local delivery partners in your neighborhood"
    }
  ];

  const stats = [
    { number: "50K+", label: "Deliveries Completed" },
    { number: "15min", label: "Average Pickup Time" },
    { number: "4.9", label: "Customer Rating" },
    { number: "24/7", label: "Support Available" }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="text-center space-y-8">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
              Hyperlocal Delivery
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-200 to-white">
                Made Simple
              </span>
            </h1>
            <p className="text-xl sm:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Send and receive packages within your city in hours, not days. 
              Connect with trusted local delivery partners for fast, secure service.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link 
                to="/book" 
                className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all transform hover:scale-105 shadow-lg flex items-center space-x-2"
              >
                <Package className="h-5 w-5" />
                <span>Book Delivery Now</span>
                <ArrowRight className="h-5 w-5" />
              </Link>
              <Link 
                to="/track" 
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all"
              >
                Track Your Package
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center space-y-2">
              <div className="text-3xl lg:text-4xl font-bold text-blue-600">{stat.number}</div>
              <div className="text-gray-600 font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-800">Why Choose QuickDrop?</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Experience the future of local delivery with our reliable, fast, and secure service
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 border border-gray-100">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="bg-blue-50 p-4 rounded-full">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-800">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* How It Works */}
      <section className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Simple steps to get your package delivered quickly and safely
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: "1",
                title: "Book Your Delivery",
                description: "Enter pickup and delivery details through our easy booking form"
              },
              {
                step: "2",
                title: "Get Matched",
                description: "We connect you with a verified local delivery partner nearby"
              },
              {
                step: "3",
                title: "Track & Receive",
                description: "Monitor your package in real-time until it reaches your doorstep"
              }
            ].map((step, index) => (
              <div key={index} className="flex flex-col items-center text-center space-y-4">
                <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-gray-800">{step.title}</h3>
                <p className="text-gray-600 leading-relaxed">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-700 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl lg:text-4xl font-bold">Ready to Get Started?</h2>
          <p className="text-xl text-blue-100">
            Join thousands of satisfied customers who trust QuickDrop for their delivery needs
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/register" 
              className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all transform hover:scale-105"
            >
              Create Account
            </Link>
            <Link 
              to="/partner-registration" 
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all"
            >
              Become a Partner
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;