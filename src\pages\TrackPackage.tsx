import React, { useState, useEffect } from 'react';
import { Search, Package, MapPin, Clock, CheckCircle, Truck, Phone } from 'lucide-react';

const TrackPackage: React.FC = () => {
  const [trackingId, setTrackingId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [trackingData, setTrackingData] = useState<any>(null);
  const [error, setError] = useState('');

  useEffect(() => {
    // Check for tracking ID in URL params
    const urlParams = new URLSearchParams(window.location.search);
    const idFromUrl = urlParams.get('id');
    if (idFromUrl) {
      setTrackingId(idFromUrl);
      handleTrack(idFromUrl);
    }
  }, []);

  const handleTrack = async (id?: string) => {
    const idToTrack = id || trackingId;
    if (!idToTrack) {
      setError('Please enter a tracking ID');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock tracking data
      const mockData = {
        trackingId: idToTrack,
        status: 'In Transit',
        estimatedDelivery: '2-3 hours',
        currentLocation: 'Sorting Hub - Downtown',
        pickupAddress: '123 Main St, Downtown',
        deliveryAddress: '456 Oak Ave, Uptown',
        packageType: 'Electronics',
        deliveryPartner: {
          name: 'John Smith',
          phone: '+****************',
          rating: 4.8
        },
        timeline: [
          {
            status: 'Order Placed',
            time: '10:30 AM',
            date: 'Today',
            completed: true,
            description: 'Delivery booking confirmed'
          },
          {
            status: 'Package Picked Up',
            time: '11:15 AM',
            date: 'Today',
            completed: true,
            description: 'Package collected from pickup location'
          },
          {
            status: 'In Transit',
            time: '11:45 AM',
            date: 'Today',
            completed: true,
            description: 'Package is on its way to destination',
            current: true
          },
          {
            status: 'Out for Delivery',
            time: 'Est. 2:30 PM',
            date: 'Today',
            completed: false,
            description: 'Package is out for final delivery'
          },
          {
            status: 'Delivered',
            time: 'Est. 3:00 PM',
            date: 'Today',
            completed: false,
            description: 'Package delivered successfully'
          }
        ]
      };

      setTrackingData(mockData);
    } catch (err) {
      setError('Failed to fetch tracking information. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleTrack();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Track Your Package</h1>
          <p className="text-gray-600 mt-2">Enter your tracking ID to get real-time updates</p>
        </div>

        {/* Search Form */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                value={trackingId}
                onChange={(e) => setTrackingId(e.target.value)}
                placeholder="Enter your tracking ID (e.g., **********)"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              />
            </div>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Tracking...</span>
                </>
              ) : (
                <>
                  <Search className="h-5 w-5" />
                  <span>Track Package</span>
                </>
              )}
            </button>
          </form>
          {error && (
            <p className="text-red-600 mt-4 text-center">{error}</p>
          )}
        </div>

        {/* Tracking Results */}
        {trackingData && (
          <div className="space-y-8">
            {/* Status Overview */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">Package Status</h2>
                  <p className="text-gray-600">Tracking ID: <span className="font-semibold">{trackingData.trackingId}</span></p>
                </div>
                <div className="mt-4 lg:mt-0">
                  <div className="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full">
                    <Truck className="h-5 w-5" />
                    <span className="font-semibold">{trackingData.status}</span>
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-6 w-6 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Current Location</p>
                    <p className="font-semibold">{trackingData.currentLocation}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-6 w-6 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Estimated Delivery</p>
                    <p className="font-semibold">{trackingData.estimatedDelivery}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Package className="h-6 w-6 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Package Type</p>
                    <p className="font-semibold">{trackingData.packageType}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Partner Info */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Delivery Partner</h3>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Truck className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-800">{trackingData.deliveryPartner.name}</p>
                    <p className="text-gray-600">Rating: {trackingData.deliveryPartner.rating}/5.0</p>
                  </div>
                </div>
                <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  <Phone className="h-4 w-4" />
                  <span>Call Partner</span>
                </button>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-6">Delivery Timeline</h3>
              <div className="space-y-6">
                {trackingData.timeline.map((event: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      event.completed 
                        ? 'bg-green-100 text-green-600' 
                        : event.current 
                        ? 'bg-blue-100 text-blue-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      {event.completed ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <div className={`w-3 h-3 rounded-full ${
                          event.current ? 'bg-blue-600' : 'bg-gray-300'
                        }`}></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className={`font-semibold ${
                          event.completed || event.current ? 'text-gray-800' : 'text-gray-500'
                        }`}>
                          {event.status}
                        </h4>
                        <span className={`text-sm ${
                          event.completed || event.current ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {event.time}, {event.date}
                        </span>
                      </div>
                      <p className={`text-sm ${
                        event.completed || event.current ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        {event.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Address Details */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <MapPin className="h-5 w-5 text-green-600" />
                  <h3 className="font-semibold text-gray-800">Pickup Address</h3>
                </div>
                <p className="text-gray-600">{trackingData.pickupAddress}</p>
              </div>
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-gray-800">Delivery Address</h3>
                </div>
                <p className="text-gray-600">{trackingData.deliveryAddress}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrackPackage;